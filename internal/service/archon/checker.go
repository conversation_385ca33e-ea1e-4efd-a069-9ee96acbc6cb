package archon

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"aisys_doc_bot/internal/config"
	"aisys_doc_bot/internal/model/archon"
)

// DAOInterface DAO接口
type DAOInterface interface {
	GetCurrentTask(taskType archon.TaskType, taskID uint) (interface{}, error)
}

// CheckerPool 检查器池
type CheckerPool struct {
	config *config.ArchonConfig
	db     *gorm.DB
	dao    DAOInterface

	// 工作线程
	workers int
	wg      sync.WaitGroup

	// 统计信息
	totalProcessed int64
	totalErrors    int64
	mu             sync.RWMutex
}

// NewCheckerPool 创建新的检查器池
func NewCheckerPool(cfg *config.ArchonConfig, db *gorm.DB) *CheckerPool {
	return &CheckerPool{
		config:  cfg,
		db:      db,
		dao:     <PERSON><PERSON><PERSON>(db),
		workers: cfg.Monitor.WorkerCount,
	}
}

// NewCheckerPoolWithDAO 创建带自定义DAO的检查器池（用于测试）
func NewCheckerPoolWithDAO(cfg *config.ArchonConfig, dao DAOInterface) *CheckerPool {
	return &CheckerPool{
		config:  cfg,
		dao:     dao,
		workers: cfg.Monitor.WorkerCount,
	}
}

// Start 启动检查器池
func (cp *CheckerPool) Start(ctx context.Context, queue *TaskQueue, notifier *NotificationService) {
	logrus.Infof("Starting checker pool with %d workers", cp.workers)

	// 启动工作线程
	for i := 0; i < cp.workers; i++ {
		cp.wg.Add(1)
		go cp.worker(ctx, i, queue, notifier)
	}

	// 等待所有工作线程完成
	cp.wg.Wait()
	logrus.Info("Checker pool stopped")
}

// worker 工作线程
func (cp *CheckerPool) worker(ctx context.Context, workerID int, queue *TaskQueue, notifier *NotificationService) {
	defer cp.wg.Done()

	logrus.Debugf("Worker %d started", workerID)

	for {
		select {
		case <-ctx.Done():
			logrus.Debugf("Worker %d stopped", workerID)
			return
		default:
			// 从队列获取任务
			item, err := queue.Dequeue(ctx)
			if err != nil {
				if err == context.Canceled {
					return
				}
				logrus.Errorf("Worker %d failed to dequeue task: %v", workerID, err)
				continue
			}

			// 处理任务
			cp.processTask(workerID, item, notifier)
		}
	}
}

// processTask 处理任务
func (cp *CheckerPool) processTask(workerID int, item *TaskItem, notifier *NotificationService) {
	start := time.Now()

	logrus.Debugf("Worker %d processing task: %s", workerID, item.ID)

	defer func() {
		duration := time.Since(start)
		logrus.Debugf("Worker %d completed task %s in %v", workerID, item.ID, duration)

		cp.mu.Lock()
		cp.totalProcessed++
		cp.mu.Unlock()
	}()

	var alert *archon.Alert
	var err error

	// 根据检查类型执行相应的检查
	switch item.CheckType {
	case archon.AlertTypeFailure:
		alert, err = cp.checkTaskFailure(item)
	case archon.AlertTypePendingTimeout:
		alert, err = cp.checkPendingTimeout(item)
	case archon.AlertTypeRuntimeTimeout:
		alert, err = cp.checkRuntimeTimeout(item)
	case archon.AlertTypeResourceWaste:
		alert, err = cp.checkResourceWaste(item)
	case archon.AlertTypeDuplicateRun:
		alert, err = cp.checkDuplicateRun(item)
	default:
		logrus.Errorf("Unknown check type: %s", item.CheckType)
		cp.mu.Lock()
		cp.totalErrors++
		cp.mu.Unlock()
		return
	}

	if err != nil {
		logrus.Errorf("Worker %d failed to check task %s: %v", workerID, item.ID, err)
		cp.mu.Lock()
		cp.totalErrors++
		cp.mu.Unlock()
		return
	}

	// 如果有告警，发送通知
	if alert != nil {
		if err := notifier.SendAlert(alert); err != nil {
			logrus.Errorf("Worker %d failed to send alert for task %s: %v", workerID, item.ID, err)
			cp.mu.Lock()
			cp.totalErrors++
			cp.mu.Unlock()
		}
	}
}

// checkTaskFailure 检查任务失败
func (cp *CheckerPool) checkTaskFailure(item *TaskItem) (*archon.Alert, error) {
	logrus.Debugf("Checking task failure for %s task %d", item.Type, item.TaskID)

	// 获取当前任务状态
	currentTask, err := cp.getCurrentTask(item.Type, item.TaskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current task: %w", err)
	}

	if currentTask == nil {
		logrus.Debugf("Task %s %d not found, skipping failure check", item.Type, item.TaskID)
		return nil, nil
	}

	// 检查任务是否失败
	if !cp.isTaskFailed(currentTask) {
		return nil, nil // 任务未失败，无需告警
	}

	// 获取任务信息
	taskName := cp.getTaskName(currentTask)
	namespace := cp.getTaskNamespace(currentTask)
	team := "unknown" // 移除团队映射功能
	user := cp.getTaskUser(currentTask)
	errorMessage := cp.getTaskErrorMessage(currentTask)

	// 创建失败告警
	alert := &archon.Alert{
		Type:      archon.AlertTypeFailure,
		Level:     archon.AlertLevelError,
		TaskType:  item.Type,
		TaskID:    item.TaskID,
		TaskName:  taskName,
		Team:      team,
		User:      user,
		Message:   errorMessage,
		Timestamp: time.Now(),
		Namespace: namespace,
		Details: map[string]string{
			"error_message": errorMessage,
			"namespace":     namespace,
		},
	}

	logrus.Infof("Task failure detected: %s task %d (%s) failed with error: %s",
		item.Type, item.TaskID, taskName, errorMessage)

	return alert, nil
}

// checkPendingTimeout 检查Pending超时
func (cp *CheckerPool) checkPendingTimeout(item *TaskItem) (*archon.Alert, error) {
	logrus.Debugf("Checking pending timeout for %s task %d", item.Type, item.TaskID)

	// 获取当前任务状态
	currentTask, err := cp.getCurrentTask(item.Type, item.TaskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current task: %w", err)
	}

	if currentTask == nil {
		logrus.Debugf("Task %s %d not found, skipping pending timeout check", item.Type, item.TaskID)
		return nil, nil
	}

	// 检查任务是否处于pending状态
	if !cp.isTaskPending(currentTask) {
		return nil, nil // 任务不是pending状态，无需检查
	}

	// 获取pending时长
	pendingDuration := cp.getTaskPendingDuration(currentTask)

	// 获取任务类型特定的配置
	taskTypeStr := string(item.Type)
	warnThreshold := cp.config.Alerts.GetTaskTypePendingTimeoutWarn(taskTypeStr)
	errorThreshold := cp.config.Alerts.GetTaskTypePendingTimeoutError(taskTypeStr)

	var level archon.AlertLevel
	var alertType string

	if pendingDuration >= errorThreshold {
		level = archon.AlertLevelError
		alertType = "error"
	} else if pendingDuration >= warnThreshold {
		level = archon.AlertLevelWarning
		alertType = "warning"
	} else {
		return nil, nil // 未超过任何阈值
	}

	// 获取任务信息
	taskName := cp.getTaskName(currentTask)
	namespace := cp.getTaskNamespace(currentTask)
	team := "unknown" // 移除团队映射功能
	user := cp.getTaskUser(currentTask)

	// 创建超时告警
	alert := &archon.Alert{
		Type:      archon.AlertTypePendingTimeout,
		Level:     level,
		TaskType:  item.Type,
		TaskID:    item.TaskID,
		TaskName:  taskName,
		Team:      team,
		User:      user,
		Message:   fmt.Sprintf("Task pending for %v", pendingDuration),
		Timestamp: time.Now(),
		Namespace: namespace,
		Details: map[string]string{
			"duration":   pendingDuration.String(),
			"alert_type": alertType,
			"namespace":  namespace,
		},
	}

	logrus.Infof("Pending timeout detected: %s task %d (%s) pending for %v",
		item.Type, item.TaskID, taskName, pendingDuration)

	return alert, nil
}

// checkRuntimeTimeout 检查运行超时
func (cp *CheckerPool) checkRuntimeTimeout(item *TaskItem) (*archon.Alert, error) {
	logrus.Debugf("Checking runtime timeout for %s task %d", item.Type, item.TaskID)

	// 获取当前任务状态
	currentTask, err := cp.getCurrentTask(item.Type, item.TaskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current task: %w", err)
	}

	if currentTask == nil {
		logrus.Debugf("Task %s %d not found, skipping runtime timeout check", item.Type, item.TaskID)
		return nil, nil
	}

	// 检查任务是否正在运行
	if !cp.isTaskRunning(currentTask) {
		return nil, nil // 任务不在运行，无需检查
	}

	// 获取当前运行时长
	currentDuration := cp.getTaskRunningDuration(currentTask)

	// 获取任务类型特定的配置
	taskTypeStr := string(item.Type)
	minHistoryRecords := cp.config.Alerts.GetMinHistoryRecords(taskTypeStr)
	runtimeMultiplier := cp.config.Alerts.GetRuntimeMultiplier(taskTypeStr)
	maxRuntimeThreshold := cp.config.Alerts.GetMaxRuntimeThreshold(taskTypeStr)

	// 检查是否超过最大运行时间阈值
	if currentDuration > maxRuntimeThreshold {
		// 超过最大运行时间，直接告警
		namespace := cp.getTaskNamespace(currentTask)
		team := "unknown" // 移除团队映射功能
		user := cp.getTaskUser(currentTask)
		taskName := cp.getTaskName(currentTask)

		alert := &archon.Alert{
			Type:      archon.AlertTypeRuntimeTimeout,
			Level:     archon.AlertLevelError,
			TaskType:  item.Type,
			TaskID:    item.TaskID,
			TaskName:  taskName,
			Team:      team,
			User:      user,
			Message:   fmt.Sprintf("Task running for %v exceeds maximum threshold %v", currentDuration, maxRuntimeThreshold),
			Timestamp: time.Now(),
			Namespace: namespace,
			Details: map[string]string{
				"duration":      currentDuration.String(),
				"max_threshold": maxRuntimeThreshold.String(),
				"alert_reason":  "max_runtime_exceeded",
				"namespace":     namespace,
			},
		}

		logrus.Infof("Maximum runtime exceeded: %s task %d (%s) running for %v (max: %v)",
			item.Type, item.TaskID, taskName, currentDuration, maxRuntimeThreshold)

		return alert, nil
	}

	// 获取任务历史记录
	taskName := cp.getTaskName(currentTask)
	dao := NewDAO(cp.db)
	histories, err := dao.GetTaskHistory(item.Type, taskName, cp.config.History.MaxHistoryRecords)
	if err != nil {
		return nil, fmt.Errorf("failed to get task history: %w", err)
	}

	// 检查是否有足够的历史记录
	if len(histories) < minHistoryRecords {
		logrus.Debugf("Not enough history records for %s task %s (%d < %d), skipping timeout check",
			item.Type, taskName, len(histories), minHistoryRecords)
		return nil, nil
	}

	// 计算平均运行时长
	avgDuration := archon.CalculateAverageRuntime(histories)
	if avgDuration == 0 {
		logrus.Debugf("No valid average runtime for %s task %s, skipping timeout check", item.Type, taskName)
		return nil, nil
	}

	// 检查是否超过阈值（平均时长的倍数）
	threshold := time.Duration(float64(avgDuration) * runtimeMultiplier)
	if currentDuration <= threshold {
		return nil, nil // 未超过阈值
	}

	// 获取任务信息
	namespace := cp.getTaskNamespace(currentTask)
	team := "unknown" // 移除团队映射功能
	user := cp.getTaskUser(currentTask)

	// 创建超时告警
	alert := &archon.Alert{
		Type:      archon.AlertTypeRuntimeTimeout,
		Level:     archon.AlertLevelWarning,
		TaskType:  item.Type,
		TaskID:    item.TaskID,
		TaskName:  taskName,
		Team:      team,
		User:      user,
		Message:   fmt.Sprintf("Task running for %v (avg: %v, threshold: %v)", currentDuration, avgDuration, threshold),
		Timestamp: time.Now(),
		Namespace: namespace,
		Details: map[string]string{
			"duration":        currentDuration.String(),
			"average_runtime": avgDuration.String(),
			"threshold":       threshold.String(),
			"multiplier":      fmt.Sprintf("%.1f", runtimeMultiplier),
			"task_type":       taskTypeStr,
			"alert_reason":    "runtime_multiplier_exceeded",
			"namespace":       namespace,
		},
	}

	logrus.Infof("Runtime timeout detected: %s task %d (%s) running for %v (threshold: %v)",
		item.Type, item.TaskID, taskName, currentDuration, threshold)

	return alert, nil
}

// checkResourceWaste 检查资源浪费
func (cp *CheckerPool) checkResourceWaste(item *TaskItem) (*archon.Alert, error) {
	logrus.Debugf("Checking resource waste for %s task %d", item.Type, item.TaskID)

	// 检查资源检查是否启用
	if !cp.config.Prometheus.ResourceCheck.Enabled {
		return nil, nil
	}

	// 获取当前任务
	currentTask, err := cp.getCurrentTask(item.Type, item.TaskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current task: %w", err)
	}

	if currentTask == nil {
		logrus.Debugf("Task %s %d not found, skipping resource check", item.Type, item.TaskID)
		return nil, nil
	}

	// 检查任务是否正在运行
	if !cp.isTaskRunning(currentTask) {
		return nil, nil // 任务不在运行，无需检查
	}

	// 创建资源检查器
	resourceChecker, err := NewResourceChecker(cp.config.Prometheus)
	if err != nil {
		return nil, fmt.Errorf("failed to create resource checker: %w", err)
	}

	// 检查资源使用情况
	resourceUsage, err := resourceChecker.CheckTaskResource(currentTask)
	if err != nil {
		return nil, fmt.Errorf("failed to check task resource: %w", err)
	}

	if resourceUsage == nil {
		return nil, nil // 无需检查或无数据
	}

	// 检查是否存在资源浪费
	cpuMinRequest := cp.config.Prometheus.ResourceCheck.CPURequestMin
	memMinRequest := cp.config.Prometheus.ResourceCheck.MemRequestMin
	cpuMaxUsage := cp.config.Prometheus.ResourceCheck.CPUUsageMax
	memMaxUsage := cp.config.Prometheus.ResourceCheck.MemUsageMax

	if !resourceUsage.IsResourceWaste(cpuMinRequest, memMinRequest, cpuMaxUsage, memMaxUsage) {
		return nil, nil // 资源使用合理
	}

	// 获取任务信息
	taskName := cp.getTaskName(currentTask)
	namespace := cp.getTaskNamespace(currentTask)
	team := "unknown" // 移除团队映射功能
	user := cp.getTaskUser(currentTask)

	// 创建资源浪费告警
	alert := &archon.Alert{
		Type:     archon.AlertTypeResourceWaste,
		Level:    archon.AlertLevelWarning,
		TaskType: item.Type,
		TaskID:   item.TaskID,
		TaskName: taskName,
		Team:     team,
		User:     user,
		Message: fmt.Sprintf("Task has inefficient resource usage - CPU: %.1f%%, Memory: %.1f%%",
			resourceUsage.CPUUsageRate, resourceUsage.MemoryUsageRate),
		Timestamp: time.Now(),
		Namespace: namespace,
		Details: map[string]string{
			"cpu_usage":   fmt.Sprintf("%.2f", resourceUsage.CPUUsage),
			"cpu_request": fmt.Sprintf("%.2f", resourceUsage.CPURequest),
			"cpu_rate":    fmt.Sprintf("%.1f", resourceUsage.CPUUsageRate),
			"mem_usage":   fmt.Sprintf("%.2f", resourceUsage.MemoryUsage),
			"mem_request": fmt.Sprintf("%.2f", resourceUsage.MemoryRequest),
			"mem_rate":    fmt.Sprintf("%.1f", resourceUsage.MemoryUsageRate),
			"pod_names":   resourceUsage.PodName,
			"namespace":   namespace,
		},
	}

	logrus.Infof("Resource waste detected: %s task %d (%s) - CPU: %.1f%% (%.2f/%.2f), Memory: %.1f%% (%.2fG/%.2fG)",
		item.Type, item.TaskID, taskName,
		resourceUsage.CPUUsageRate, resourceUsage.CPUUsage, resourceUsage.CPURequest,
		resourceUsage.MemoryUsageRate, resourceUsage.MemoryUsage, resourceUsage.MemoryRequest)

	return alert, nil
}

// checkDuplicateRun 检查重复运行
func (cp *CheckerPool) checkDuplicateRun(item *TaskItem) (*archon.Alert, error) {
	logrus.Debugf("Checking duplicate run for %s task %d", item.Type, item.TaskID)

	// 获取当前任务
	currentTask, err := cp.getCurrentTask(item.Type, item.TaskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current task: %w", err)
	}

	if currentTask == nil {
		logrus.Debugf("Task %s %d not found, skipping duplicate check", item.Type, item.TaskID)
		return nil, nil
	}

	// 获取任务参数
	params := cp.getTaskParams(currentTask)
	if params == nil {
		logrus.Debugf("Task %s %d has no params, skipping duplicate check", item.Type, item.TaskID)
		return nil, nil
	}

	// 生成参数哈希
	paramsHash := cp.generateParamsHash(params)
	if paramsHash == "" {
		logrus.Debugf("Failed to generate params hash for task %s %d", item.Type, item.TaskID)
		return nil, nil
	}

	// 检查时间窗口内的重复任务
	timeWindow := cp.config.DuplicateCheck.GetTimeWindow()
	dao := NewDAO(cp.db)
	duplicateCount, err := dao.GetDuplicateTasksByParams(item.Type, paramsHash, timeWindow)
	if err != nil {
		return nil, fmt.Errorf("failed to check duplicate tasks: %w", err)
	}

	// 如果只有1个（当前任务），则不是重复运行
	if duplicateCount <= 1 {
		return nil, nil
	}

	// 获取任务信息
	taskName := cp.getTaskName(currentTask)
	namespace := cp.getTaskNamespace(currentTask)
	team := "unknown" // 移除团队映射功能
	user := cp.getTaskUser(currentTask)

	// 创建重复运行告警
	alert := &archon.Alert{
		Type:      archon.AlertTypeDuplicateRun,
		Level:     archon.AlertLevelWarning,
		TaskType:  item.Type,
		TaskID:    item.TaskID,
		TaskName:  taskName,
		Team:      team,
		User:      user,
		Message:   fmt.Sprintf("Task has %d duplicate runs in the last %v", duplicateCount, timeWindow),
		Timestamp: time.Now(),
		Namespace: namespace,
		Details: map[string]string{
			"duplicate_count": fmt.Sprintf("%d", duplicateCount),
			"time_window":     timeWindow.String(),
			"params_hash":     paramsHash,
			"namespace":       namespace,
		},
	}

	logrus.Infof("Duplicate run detected: %s task %d (%s) has %d duplicates in %v",
		item.Type, item.TaskID, taskName, duplicateCount, timeWindow)

	return alert, nil
}

// GetStats 获取检查器池统计信息
func (cp *CheckerPool) GetStats() map[string]interface{} {
	cp.mu.RLock()
	defer cp.mu.RUnlock()

	return map[string]interface{}{
		"workers":         cp.workers,
		"total_processed": cp.totalProcessed,
		"total_errors":    cp.totalErrors,
	}
}

// getCurrentTask 获取当前任务
func (cp *CheckerPool) getCurrentTask(taskType archon.TaskType, taskID uint) (interface{}, error) {
	if cp.dao != nil {
		return cp.dao.GetCurrentTask(taskType, taskID)
	}

	dao := NewDAO(cp.db)
	return dao.GetCurrentTask(taskType, taskID)
}

// isTaskFailed 检查任务是否失败
func (cp *CheckerPool) isTaskFailed(task interface{}) bool {
	switch t := task.(type) {
	case *archon.DevJobInstance:
		return t.IsFailed()
	case *archon.JobDeploymentInstance:
		return t.IsFailed()
	case *archon.RunDeploymentInstance:
		return t.IsFailed()
	default:
		return false
	}
}

// getTaskName 获取任务名称
func (cp *CheckerPool) getTaskName(task interface{}) string {
	switch t := task.(type) {
	case *archon.DevJobInstance:
		return t.JobName
	case *archon.JobDeploymentInstance:
		return t.JobName
	case *archon.RunDeploymentInstance:
		return t.DeploymentName
	default:
		return "unknown"
	}
}

// getTaskNamespace 获取任务命名空间
func (cp *CheckerPool) getTaskNamespace(task interface{}) string {
	switch t := task.(type) {
	case *archon.DevJobInstance:
		return t.JobNamespace
	case *archon.JobDeploymentInstance:
		return t.JobNamespace
	case *archon.RunDeploymentInstance:
		return t.Namespace
	default:
		return ""
	}
}

// getTaskUser 获取任务负责人
func (cp *CheckerPool) getTaskUser(task interface{}) string {
	switch t := task.(type) {
	case *archon.DevJobInstance:
		return t.Developer
	case *archon.JobDeploymentInstance:
		return t.GetDeployer()
	case *archon.RunDeploymentInstance:
		return t.GetDeployer()
	default:
		return ""
	}
}

// getTaskErrorMessage 获取任务错误信息
func (cp *CheckerPool) getTaskErrorMessage(task interface{}) string {
	switch t := task.(type) {
	case *archon.DevJobInstance:
		return t.ErrorMessage
	case *archon.JobDeploymentInstance:
		return t.ErrorMessage
	case *archon.RunDeploymentInstance:
		return t.ErrorMessage
	default:
		return ""
	}
}

// isTaskPending 检查任务是否处于pending状态
func (cp *CheckerPool) isTaskPending(task interface{}) bool {
	switch t := task.(type) {
	case *archon.DevJobInstance:
		return t.IsPending()
	case *archon.JobDeploymentInstance:
		return t.IsPending()
	case *archon.RunDeploymentInstance:
		return t.IsPending()
	default:
		return false
	}
}

// getTaskPendingDuration 获取任务pending时长
func (cp *CheckerPool) getTaskPendingDuration(task interface{}) time.Duration {
	switch t := task.(type) {
	case *archon.DevJobInstance:
		return t.GetPendingDuration()
	case *archon.JobDeploymentInstance:
		return t.GetPendingDuration()
	case *archon.RunDeploymentInstance:
		return t.GetPendingDuration()
	default:
		return 0
	}
}

// isTaskRunning 检查任务是否正在运行
func (cp *CheckerPool) isTaskRunning(task interface{}) bool {
	switch t := task.(type) {
	case *archon.DevJobInstance:
		return t.IsRunning()
	case *archon.JobDeploymentInstance:
		return t.IsRunning()
	case *archon.RunDeploymentInstance:
		return t.IsRunning()
	default:
		return false
	}
}

// getTaskRunningDuration 获取任务运行时长
func (cp *CheckerPool) getTaskRunningDuration(task interface{}) time.Duration {
	switch t := task.(type) {
	case *archon.DevJobInstance:
		return t.GetRunningDuration()
	case *archon.JobDeploymentInstance:
		return t.GetRunningDuration()
	case *archon.RunDeploymentInstance:
		return t.GetRunningDuration()
	default:
		return 0
	}
}

// getTaskParams 获取任务参数
func (cp *CheckerPool) getTaskParams(task interface{}) json.RawMessage {
	switch t := task.(type) {
	case *archon.DevJobInstance:
		return t.Params
	case *archon.JobDeploymentInstance:
		return t.Params
	case *archon.RunDeploymentInstance:
		// DAG任务的参数在Deployment表中
		if t.Deployment != nil {
			return t.Deployment.Params
		}
		return nil
	default:
		return nil
	}
}

// generateParamsHash 生成参数哈希
func (cp *CheckerPool) generateParamsHash(params json.RawMessage) string {
	if params == nil {
		return ""
	}

	// 标准化JSON格式
	var obj interface{}
	if err := json.Unmarshal(params, &obj); err != nil {
		logrus.Errorf("Failed to unmarshal params: %v", err)
		return ""
	}

	// 重新序列化，确保格式一致
	normalized, err := json.Marshal(obj)
	if err != nil {
		logrus.Errorf("Failed to marshal normalized params: %v", err)
		return ""
	}

	// 计算MD5哈希
	hash := md5.Sum(normalized)
	return fmt.Sprintf("%x", hash)
}
